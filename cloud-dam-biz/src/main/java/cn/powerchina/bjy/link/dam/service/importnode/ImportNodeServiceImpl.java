package cn.powerchina.bjy.link.dam.service.importnode;

import cn.hutool.core.collection.CollUtil;
import cn.powerchina.bjy.cloud.framework.common.exception.ErrorCode;
import cn.powerchina.bjy.cloud.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.powerchina.bjy.link.dam.dal.dataobject.importfile.ImportFileDO;
import cn.powerchina.bjy.link.dam.dal.mysql.importfile.ImportFileMapper;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

import cn.powerchina.bjy.link.dam.controller.admin.importnode.vo.*;
import cn.powerchina.bjy.link.dam.dal.dataobject.importnode.ImportNodeDO;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageParam;
import cn.powerchina.bjy.cloud.framework.common.util.object.BeanUtils;

import cn.powerchina.bjy.link.dam.dal.mysql.importnode.ImportNodeMapper;

import static cn.powerchina.bjy.cloud.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.powerchina.bjy.cloud.framework.common.util.collection.CollectionUtils.convertList;
import static cn.powerchina.bjy.cloud.framework.common.util.collection.CollectionUtils.diffList;
import static cn.powerchina.bjy.link.dam.enums.ErrorCodeConstants.*;

/**
 * 节点信息 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class ImportNodeServiceImpl implements ImportNodeService {

    @Resource
    private ImportNodeMapper importNodeMapper;

    @Resource
    private ImportFileMapper importFileMapper;

    @Override
    public Long createImportNode(ImportNodeSaveReqVO createReqVO) {
        // 插入
        ImportNodeDO importNode = BeanUtils.toBean(createReqVO, ImportNodeDO.class);
        importNodeMapper.insert(importNode);

        // 返回
        return importNode.getId();
    }

    @Override
    public void updateImportNode(ImportNodeSaveReqVO updateReqVO) {
        // 校验存在
        validateImportNodeExists(updateReqVO.getId());
        // 更新
        ImportNodeDO updateObj = BeanUtils.toBean(updateReqVO, ImportNodeDO.class);
        importNodeMapper.updateById(updateObj);
    }

    @Override
    public void deleteImportNode(Long id) {
        // 校验存在
        validateImportNodeExists(id);
        Long count=importNodeMapper.selectCount(new LambdaQueryWrapperX<ImportNodeDO>()
                .eq(ImportNodeDO::getParentId,id)
        );
        if(count>0){
            throw exception(new ErrorCode(500,"该节点下有子节点，不可删除！"));
        }
        Long countFile=importFileMapper.selectCount(new LambdaQueryWrapperX<ImportFileDO>()
                .eq(ImportFileDO::getNodeId,id)
        );
        if(countFile>0){
            throw exception(new ErrorCode(500,"该节点下有excel文件，不可删除！"));
        }
        // 删除
        importNodeMapper.deleteById(id);
    }

    @Override
        public void deleteImportNodeListByIds(List<Long> ids) {
        // 删除
        importNodeMapper.deleteBatchIds(ids);
        }


    private void validateImportNodeExists(Long id) {
        if (importNodeMapper.selectById(id) == null) {
//            throw exception(IMPORT_NODE_NOT_EXISTS);
        }
    }

//    @Override
//    public List<ImportNodeRespVO> getNodeByProjectId(Long projectId) {
//        List<ImportNodeDO> importNodeList = importNodeMapper.selectList(new LambdaQueryWrapperX<ImportNodeDO>()
//                .eq(ImportNodeDO::getProjectId, projectId)
//                .orderByAsc(ImportNodeDO::getId)
//        );
//        List<ImportNodeRespVO> list = BeanUtils.toBean(importNodeList, ImportNodeRespVO.class);
//        List<ImportNodeRespVO> list1=new ArrayList<>();
//        List<ImportNodeRespVO> list2=new ArrayList<>();
//        List<ImportNodeRespVO> list3=new ArrayList<>();
//        List<ImportNodeRespVO> list4=new ArrayList<>();
//        List<ImportNodeRespVO> list5=new ArrayList<>();
//        List<ImportNodeRespVO> list6=new ArrayList<>();
//        for(ImportNodeRespVO vo:list){
//            if(vo.getHierarchy()==6){
//                list6.add(vo);
//            }
//        }
//        for(ImportNodeRespVO vo:list){
//            if(vo.getHierarchy()==5){
//                list5.add(vo);
//            }
//        }
//        setChildren(list5, list6);
//        for(ImportNodeRespVO vo:list){
//            if(vo.getHierarchy()==4){
//                list4.add(vo);
//            }
//        }
//        setChildren(list4,list5);
//        for(ImportNodeRespVO vo:list){
//            if(vo.getHierarchy()==3){
//                list3.add(vo);
//            }
//        }
//        setChildren(list3,list4);
//        for(ImportNodeRespVO vo:list){
//            if(vo.getHierarchy()==2){
//                list2.add(vo);
//            }
//        }
//        setChildren(list2,list3);
//        for(ImportNodeRespVO vo:list){
//            if(vo.getHierarchy()==1){
//                list1.add(vo);
//            }
//        }
//        setChildren(list1,list2);
//        return list1;
//    }
//
//    private List<ImportNodeRespVO> setChildren(List<ImportNodeRespVO> parent, List<ImportNodeRespVO> children){
//        for(ImportNodeRespVO parentVO:parent){
//            for(ImportNodeRespVO childrenVO:children){
//                if(parentVO.getId().equals(childrenVO.getParentId())){
//                    parentVO.getChildren().add(childrenVO);
//                }
//            }
//        }
//        return parent;
//    }




    @Override
    public List<ImportNodeRespVO> getNodeByProjectId(Long projectId) {
        List<ImportNodeDO> importNodeList = importNodeMapper.selectList(new LambdaQueryWrapperX<ImportNodeDO>()
                .eq(ImportNodeDO::getProjectId, projectId)
                .orderByAsc(ImportNodeDO::getId));

        List<ImportNodeRespVO> nodeList = BeanUtils.toBean(importNodeList, ImportNodeRespVO.class);
        if (nodeList.isEmpty()) {
            return Collections.emptyList();
        }

         Map<Long, List<ImportNodeRespVO>> levelMap = nodeList.stream()
                .collect(Collectors.groupingBy(ImportNodeRespVO::getHierarchy));

        for (int i = 6; i > 1; i--) {
            List<ImportNodeRespVO> currentLevel = levelMap.getOrDefault((long) i,Collections.emptyList());
            List<ImportNodeRespVO> parentLevel = levelMap.getOrDefault((long) (i - 1),Collections.emptyList());
            buildChildNodes(parentLevel, currentLevel);
        }

        return levelMap.getOrDefault(1L, Collections.emptyList());
    }

    private void buildChildNodes(List<ImportNodeRespVO> parents, List<ImportNodeRespVO> children) {
        if (children.isEmpty()) {
            return;
        }

        Map<Long, List<ImportNodeRespVO>> childMap = new HashMap<>();
        for (ImportNodeRespVO child : children) {
            childMap.computeIfAbsent(child.getParentId(), k -> new ArrayList<>()).add(child);
        }

        for (ImportNodeRespVO parent : parents) {
            List<ImportNodeRespVO> childList = childMap.getOrDefault(parent.getId(), Collections.emptyList());
            parent.setChildren(childList);
        }
    }


    @Override
    public PageResult<ImportNodeDO> getImportNodePage(ImportNodePageReqVO pageReqVO) {
        return importNodeMapper.selectPage(pageReqVO);
    }

}