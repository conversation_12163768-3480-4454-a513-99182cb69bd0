package cn.powerchina.bjy.link.dam.controller.admin.pointdata.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;
import java.util.List;

import static cn.powerchina.bjy.cloud.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

/**
 * @Description: 监测图形-分布图的参数
 * @Author: yangjingtao
 * @CreateDate: 2025/06/26
 */
@Schema(description = "管理后台-监测图形-分布图 Request VO")
@Data
@ToString(callSuper = true)
public class PointDataDistributionChartReqVO {

    @Schema(description = "项目id", example = "19338")
    @NotNull(message = "项目不能为空")
    private Long projectId;

    @Schema(description = "测点id列表", example = "24279")
    @NotNull(message = "测点id不能为空")
    private List<Long> pointIdList;

    @Schema(description = "分量id列表")
    @NotNull(message = "分量id不能为空")
    private List<Long> instrumentModelIdList;

    @Schema(description = "时间间隔单位（1：日，2：小时）")
    @NotNull(message = "时间间隔单位不能为空")
    private Integer timeIntervalUnit;

    @Schema(description = "读取日期列表：yyyy-MM-dd HH:mm:ss")
    @NotNull(message = "读取日期不能为空")
    private List<Long> pointTimeList;

    @Schema(description = "采集类型(1：自动化采集，2：人工录入）", example = "1")
    private List<Integer> dataTypeList;

    @Schema(description = "数据状态(0：未判定，1：正常数据，2：异常，3：错误数据）", example = "1")
    private List<Integer> dataStatusList;

    @Schema(description = "审核状态（0：未审核；1：审核通过；2：审核不通过）", example = "1")
    private List<Integer> reviewStatusList;
}
