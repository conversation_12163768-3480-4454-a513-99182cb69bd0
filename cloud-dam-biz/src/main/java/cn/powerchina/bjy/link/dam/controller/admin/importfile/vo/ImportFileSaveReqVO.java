package cn.powerchina.bjy.link.dam.controller.admin.importfile.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.io.File;
import java.util.*;
import jakarta.validation.constraints.*;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.multipart.MultipartFile;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 导入信息新增/修改 Request VO")
@Data
public class ImportFileSaveReqVO {

    @Schema(description = "主键id", requiredMode = Schema.RequiredMode.REQUIRED, example = "3696")
    private Long id;

    @Schema(description = "项目id", example = "8231")
    private Long projectId;

    @Schema(description = "节点id", example = "9170")
    private Long nodeId;

    @Schema(description = "excel文件名称", example = "芋艿")
    private String fileName;

    @Schema(description = "excel文件地址")
    private String filePath;

    @Schema(description = "excel文件")
    private MultipartFile file;

    @Schema(description = "关联测点数")
    private Integer pointNumber;

    @Schema(description = "最新上传时间")
    private LocalDateTime uploadTime;

    @Schema(description = "指定导入开始时间")
    private LocalDateTime startTime;

    @Schema(description = "指定导入结束时间")
    private LocalDateTime endTime;

    @Schema(description = "导入新数据,0否，1是")
    private Boolean uploadNew;

}