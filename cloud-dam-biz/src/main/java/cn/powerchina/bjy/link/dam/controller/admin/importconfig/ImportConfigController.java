package cn.powerchina.bjy.link.dam.controller.admin.importconfig;

import cn.powerchina.bjy.link.dam.controller.admin.importfile.vo.ImportFileSaveReqVO;
import org.springframework.web.bind.annotation.*;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import jakarta.validation.constraints.*;
import jakarta.validation.*;
import jakarta.servlet.http.*;
import java.util.*;
import java.io.IOException;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageParam;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult;
import cn.powerchina.bjy.cloud.framework.common.util.object.BeanUtils;
import static cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult.success;

import cn.powerchina.bjy.cloud.framework.excel.core.util.ExcelUtils;

import cn.powerchina.bjy.cloud.framework.apilog.core.annotation.ApiAccessLog;
import static cn.powerchina.bjy.cloud.framework.apilog.core.enums.OperateTypeEnum.*;

import cn.powerchina.bjy.link.dam.controller.admin.importconfig.vo.*;
import cn.powerchina.bjy.link.dam.dal.dataobject.importconfig.ImportConfigDO;
import cn.powerchina.bjy.link.dam.service.importconfig.ImportConfigService;

@Tag(name = "管理后台 - 导入配置")
@RestController
@RequestMapping("/dam/import-config")
@Validated
public class ImportConfigController {

    @Resource
    private ImportConfigService importConfigService;

    @PostMapping("/create")
    @Operation(summary = "创建导入配置")
//    @PreAuthorize("@ss.hasPermission('dam:import-config:create')")
    public CommonResult<Long> createImportConfig(ImportFileSaveReqVO createReqVO) {
        return success(importConfigService.createImportConfig(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新导入配置")
    @PreAuthorize("@ss.hasPermission('dam:import-config:update')")
    public CommonResult<Boolean> updateImportConfig(@Valid @RequestBody ImportConfigSaveReqVO updateReqVO) {
        importConfigService.updateImportConfig(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除导入配置")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('dam:import-config:delete')")
    public CommonResult<Boolean> deleteImportConfig(@RequestParam("id") Long id) {
        importConfigService.deleteImportConfig(id);
        return success(true);
    }

    @DeleteMapping("/delete-list")
    @Parameter(name = "ids", description = "编号", required = true)
    @Operation(summary = "批量删除导入配置")
                @PreAuthorize("@ss.hasPermission('dam:import-config:delete')")
    public CommonResult<Boolean> deleteImportConfigList(@RequestParam("ids") List<Long> ids) {
        importConfigService.deleteImportConfigListByIds(ids);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得导入配置")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('dam:import-config:query')")
    public CommonResult<ImportConfigRespVO> getImportConfig(@RequestParam("id") Long id) {
        ImportConfigDO importConfig = importConfigService.getImportConfig(id);
        return success(BeanUtils.toBean(importConfig, ImportConfigRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得导入配置分页")
    @PreAuthorize("@ss.hasPermission('dam:import-config:query')")
    public CommonResult<PageResult<ImportConfigRespVO>> getImportConfigPage(@Valid ImportConfigPageReqVO pageReqVO) {
        PageResult<ImportConfigDO> pageResult = importConfigService.getImportConfigPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, ImportConfigRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出导入配置 Excel")
    @PreAuthorize("@ss.hasPermission('dam:import-config:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportImportConfigExcel(@Valid ImportConfigPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<ImportConfigDO> list = importConfigService.getImportConfigPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "导入配置.xls", "数据", ImportConfigRespVO.class,
                        BeanUtils.toBean(list, ImportConfigRespVO.class));
    }

}