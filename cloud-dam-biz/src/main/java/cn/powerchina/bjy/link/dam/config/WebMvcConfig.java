package cn.powerchina.bjy.link.dam.config;

import org.springframework.context.annotation.Configuration;
import org.springframework.core.convert.converter.Converter;
import org.springframework.format.FormatterRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;

/**
 * @Description: Web MVC 配置
 * @Author: yangjingtao
 * @CreateDate: 2025/01/01
 */
@Configuration
public class WebMvcConfig implements WebMvcConfigurer {

    @Override
    public void addFormatters(FormatterRegistry registry) {
        // 添加时间戳字符串到LocalDateTime的转换器
        registry.addConverter(new StringToLocalDateTimeConverter());
    }

    /**
     * 时间戳字符串转LocalDateTime转换器
     */
    public static class StringToLocalDateTimeConverter implements Converter<String, LocalDateTime> {
        @Override
        public LocalDateTime convert(String source) {
            if (source == null || source.trim().isEmpty()) {
                return null;
            }
            
            try {
                // 尝试解析为时间戳（毫秒）
                long timestamp = Long.parseLong(source.trim());
                return LocalDateTime.ofInstant(Instant.ofEpochMilli(timestamp), ZoneId.systemDefault());
            } catch (NumberFormatException e) {
                // 如果不是时间戳，可以在这里添加其他格式的解析
                throw new IllegalArgumentException("无法解析时间格式: " + source, e);
            }
        }
    }
}
