package cn.powerchina.bjy.link.dam.controller.admin.importnode;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageParam;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult;
import cn.powerchina.bjy.cloud.framework.common.util.object.BeanUtils;
import static cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult.success;

import cn.powerchina.bjy.cloud.framework.excel.core.util.ExcelUtils;

import cn.powerchina.bjy.cloud.framework.apilog.core.annotation.ApiAccessLog;
import static cn.powerchina.bjy.cloud.framework.apilog.core.enums.OperateTypeEnum.*;

import cn.powerchina.bjy.link.dam.controller.admin.importnode.vo.*;
import cn.powerchina.bjy.link.dam.dal.dataobject.importnode.ImportNodeDO;
import cn.powerchina.bjy.link.dam.service.importnode.ImportNodeService;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import org.springframework.web.bind.annotation.*;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import java.util.*;
import java.io.IOException;




@Tag(name = "管理后台 - 节点信息")
@RestController
@RequestMapping("/dam/import-node")
@Validated
public class ImportNodeController {

    @Resource
    private ImportNodeService importNodeService;

    @PostMapping("/create")
    @Operation(summary = "创建节点信息")
//    @PreAuthorize("@ss.hasPermission('dam:import-node:create')")
    public CommonResult<Long> createImportNode(@Valid @RequestBody ImportNodeSaveReqVO createReqVO) {
        return success(importNodeService.createImportNode(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新节点信息")
//    @PreAuthorize("@ss.hasPermission('dam:import-node:update')")
    public CommonResult<Boolean> updateImportNode(@Valid @RequestBody ImportNodeSaveReqVO updateReqVO) {
        importNodeService.updateImportNode(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除节点信息")
    @Parameter(name = "id", description = "编号", required = true)
//    @PreAuthorize("@ss.hasPermission('dam:import-node:delete')")
    public CommonResult<Boolean> deleteImportNode(@RequestParam("id") Long id) {
        importNodeService.deleteImportNode(id);
        return success(true);
    }

//    @DeleteMapping("/delete-list")
//    @Parameter(name = "ids", description = "编号", required = true)
//    @Operation(summary = "批量删除节点信息")
//                @PreAuthorize("@ss.hasPermission('dam:import-node:delete')")
//    public CommonResult<Boolean> deleteImportNodeList(@RequestParam("ids") List<Long> ids) {
//        importNodeService.deleteImportNodeListByIds(ids);
//        return success(true);
//    }

    @GetMapping("/getNodeByProjectId")
    @Operation(summary = "获得节点信息")
    @Parameter(name = "projectId", description = "项目id", required = true, example = "1024")
//    @PreAuthorize("@ss.hasPermission('dam:import-node:query')")
    public CommonResult<List<ImportNodeRespVO>> getNodeByProjectId(@RequestParam("projectId") Long projectId) {
        List<ImportNodeRespVO> importNode = importNodeService.getNodeByProjectId(projectId);
        return success(importNode);
    }

    @GetMapping("/page")
    @Operation(summary = "获得节点信息分页")
    @PreAuthorize("@ss.hasPermission('dam:import-node:query')")
    public CommonResult<PageResult<ImportNodeRespVO>> getImportNodePage(@Valid ImportNodePageReqVO pageReqVO) {
        PageResult<ImportNodeDO> pageResult = importNodeService.getImportNodePage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, ImportNodeRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出节点信息 Excel")
    @PreAuthorize("@ss.hasPermission('dam:import-node:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportImportNodeExcel(@Valid ImportNodePageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<ImportNodeDO> list = importNodeService.getImportNodePage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "节点信息.xls", "数据", ImportNodeRespVO.class,
                        BeanUtils.toBean(list, ImportNodeRespVO.class));
    }

}