package cn.powerchina.bjy.link.dam.service.importconfig;

import cn.hutool.core.collection.CollUtil;
import cn.powerchina.bjy.cloud.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.powerchina.bjy.link.dam.controller.admin.importfile.vo.ImportFileSaveReqVO;
import cn.powerchina.bjy.link.dam.dal.dataobject.instrumentmodel.InstrumentModelDO;
import cn.powerchina.bjy.link.dam.dal.dataobject.point.PointDO;
import cn.powerchina.bjy.link.dam.dal.mysql.instrumentmodel.InstrumentModelMapper;
import cn.powerchina.bjy.link.dam.dal.mysql.point.PointMapper;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.stereotype.Service;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.transaction.annotation.Transactional;

import java.io.*;
import java.util.*;
import cn.powerchina.bjy.link.dam.controller.admin.importconfig.vo.*;
import cn.powerchina.bjy.link.dam.dal.dataobject.importconfig.ImportConfigDO;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageParam;
import cn.powerchina.bjy.cloud.framework.common.util.object.BeanUtils;

import cn.powerchina.bjy.link.dam.dal.mysql.importconfig.ImportConfigMapper;
import org.springframework.web.multipart.MultipartFile;

import static cn.powerchina.bjy.cloud.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.powerchina.bjy.cloud.framework.common.util.collection.CollectionUtils.convertList;
import static cn.powerchina.bjy.cloud.framework.common.util.collection.CollectionUtils.diffList;
import static cn.powerchina.bjy.link.dam.enums.ErrorCodeConstants.*;

/**
 * 导入配置 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class ImportConfigServiceImpl implements ImportConfigService {

    @Resource
    private ImportConfigMapper importConfigMapper;

    @Resource
    private PointMapper pointMapper;

    @Resource
    private InstrumentModelMapper instrumentModelMapper;

    @Override
    public Long createImportConfig(ImportFileSaveReqVO createReqVO) {
        try {
            Workbook workbook = new XSSFWorkbook(createReqVO.getFile().getInputStream()); // 对于xlsx文件使用XSSFWorkbook，对于xls文件使用HSSFWorkbook
            List<ImportConfigDO> list= new ArrayList<>();
            // 获取所有工作表的名称
            for (int i = 0; i < workbook.getNumberOfSheets(); i++) {
                Sheet sheet = workbook.getSheetAt(i);
                List<PointDO> pointDOList = pointMapper.selectList(new LambdaQueryWrapperX<PointDO>()
                        .eq(PointDO::getProjectId, createReqVO.getProjectId())
                        .eq(PointDO::getPointName, sheet.getSheetName())
                );
                if (pointDOList!=null && !pointDOList.isEmpty()) {
                    for(PointDO pointDO:pointDOList){
                        List<InstrumentModelDO> instrumentModelDOList = instrumentModelMapper.selectList(new LambdaQueryWrapperX<InstrumentModelDO>()
                                .eq(InstrumentModelDO::getInstrumentId, pointDO.getInstrumentId())
                                .eq(InstrumentModelDO::getProjectId, pointDO.getProjectId())
                        );
                        if(instrumentModelDOList!=null && !instrumentModelDOList.isEmpty()){
                            for(InstrumentModelDO instrumentModelDO:instrumentModelDOList){
                                ImportConfigDO importConfigDO = new ImportConfigDO();
                                importConfigDO.setImportId(createReqVO.getId());
                                importConfigDO.setSheetName(sheet.getSheetName()+"_"+String.valueOf(i+1));
                                importConfigDO.setPointId(pointDO.getId());
                                importConfigDO.setIsAutomation(0);
                                importConfigDO.setInstrumentModelId(instrumentModelDO.getId());
                                importConfigDO.setThingIdentity(instrumentModelDO.getThingIdentity());
                                list.add(importConfigDO);
                            }
                        }
                    }
                }else{
                    ImportConfigDO importConfigDO = new ImportConfigDO();
                    importConfigDO.setImportId(createReqVO.getId());
                    importConfigDO.setSheetName(sheet.getSheetName()+"_"+String.valueOf(i+1));
                    list.add(importConfigDO);
                }
            }
            importConfigMapper.insertBatch( list);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return 1L;
    }

    @Override
    public void updateImportConfig(ImportConfigSaveReqVO updateReqVO) {
        // 校验存在
        validateImportConfigExists(updateReqVO.getId());
        // 更新
        ImportConfigDO updateObj = BeanUtils.toBean(updateReqVO, ImportConfigDO.class);
        importConfigMapper.updateById(updateObj);
    }

    @Override
    public void deleteImportConfig(Long id) {
        // 校验存在
        validateImportConfigExists(id);
        // 删除
        importConfigMapper.deleteById(id);
    }

    @Override
        public void deleteImportConfigListByIds(List<Long> ids) {
        // 删除
        importConfigMapper.deleteBatchIds(ids);
        }


    private void validateImportConfigExists(Long id) {
        if (importConfigMapper.selectById(id) == null) {
//            throw exception(IMPORT_CONFIG_NOT_EXISTS);
        }
    }

    @Override
    public ImportConfigDO getImportConfig(Long id) {
        return importConfigMapper.selectById(id);
    }

    @Override
    public PageResult<ImportConfigDO> getImportConfigPage(ImportConfigPageReqVO pageReqVO) {
        return importConfigMapper.selectPage(pageReqVO);
    }

}