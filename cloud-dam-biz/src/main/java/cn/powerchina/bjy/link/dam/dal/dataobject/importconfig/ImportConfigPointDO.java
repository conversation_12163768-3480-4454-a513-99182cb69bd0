package cn.powerchina.bjy.link.dam.dal.dataobject.importconfig;

import cn.powerchina.bjy.cloud.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

/**
 * 导入配置_测点 DO
 *
 * <AUTHOR>
 */
@TableName("dam_import_config_point")
@KeySequence("dam_import_config_point_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ImportConfigPointDO extends BaseDO {

    /**
     * 主键id
     */
    @TableId
    private Long id;
    /**
     * 导入信息id
     */
    private Long sheetConfigId;
    /**
     * 测点id
     */
    private Long pointId;

}