package cn.powerchina.bjy.link.dam.controller.admin.pointdata.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * @Description: 监测图形-过程线的响应
 * @Author: ya<PERSON><PERSON><PERSON><PERSON>
 * @CreateDate: 2025/06/26
 */
@Schema(description = "管理后台-监测图形-过程线 Response VO")
@Data
public class PointDataProcessLineResVO {

    private PointDataProcessLineTitleVO title;

    private PointDataProcessLineTootipVO tootip;

    private PointDataProcessLineLegendVO legend;

    @JsonProperty("xAxis")
    private PointDataProcessLineXaxisVO xAxis;

    @JsonProperty("yAxis")
    private List<PointDataProcessLineYaxisVO> yAxis;

    private List<PointDataProcessLineSeriesVO> series;
}