package cn.powerchina.bjy.link.dam.controller.admin.pointdata;

import cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.common.util.object.BeanUtils;
import cn.powerchina.bjy.link.dam.controller.admin.pointdata.bo.PointDataBO;
import cn.powerchina.bjy.link.dam.controller.admin.pointdata.vo.*;
import cn.powerchina.bjy.link.dam.service.pointdata.PointDataService;
import cn.powerchina.bjy.link.iot.model.DeviceDataTransportModel;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import static cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 测点数据")
@RestController
@RequestMapping("/dam/point/data")
@Validated
public class PointDataController {

    @Resource
    private PointDataService pointDataService;

    @PostMapping("/create")
    @Operation(summary = "创建测点数据")
//    @PreAuthorize("@ss.hasPermission('dam:point-data:create')")
    public CommonResult<Long> createPointData(@Valid @RequestBody DeviceDataTransportModel deviceDataTransportModel) {
        pointDataService.saveBatchPointData(deviceDataTransportModel);
        return success(1L);
    }

    @GetMapping("/modelvalue")
    @Operation(summary = "测点数据折线图")
//    @PreAuthorize("@ss.hasPermission('dam:point-data:query')")
    public CommonResult<PointDataModelRespVO> getPointDataList(@Valid PointDataReqVO pointDataReqVO) {
        PointDataModelRespVO modelValueList = pointDataService.getModelValueList(pointDataReqVO);
        return success(modelValueList);
    }

    @GetMapping("/statistics")
    @Operation(summary = "获得测点统计数据")
//    @PreAuthorize("@ss.hasPermission('dam:point-data:query')")
    public CommonResult<List<PointDataRespVO>> getPointDataStatistics(@Valid PointDataStatisticsVO statisticsVO) {
        List<PointDataBO> pageResult = pointDataService.pointStatistics(statisticsVO);
//        List<PointDataRespVO> list=BeanUtils.toBean(pageResult, PointDataRespVO.class);
//        if(!CollectionUtils.isEmpty(list)) {
//            list.stream().forEach(item ->{
//                if(!StringUtils.isEmpty(item.getYearAndMonth())&&item.getYearAndMonth().length()>6) {
//                    item.setMonth(item.getYearAndMonth().substring(5,7));
//                }
//            });
//        }
        return success(BeanUtils.toBean(pageResult, PointDataRespVO.class));
    }

    @GetMapping("/export")
    @Operation(summary = "测点数据导出 Excel")
    public void exportExcel(@Valid PointDataStatisticsVO statisticsVO,
                            HttpServletResponse response) {
        pointDataService.exportExcel(response, statisticsVO);
    }

    @GetMapping("/page")
    @Operation(summary = "获得测点数据分页")
//    @PreAuthorize("@ss.hasPermission('dam:point-data:query')")
    public CommonResult<PageResult<PointDataRespVO>> getPointDataPage(@Valid PointDataPageReqVO pageReqVO) {
        PageResult<PointDataBO> pageResult = pointDataService.pointStatisticsPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, PointDataRespVO.class));
    }

    @GetMapping("/processline")
    @Operation(summary = "数据整编--监测图形--过程线")
    public CommonResult<List<PointDataProcessLineResVO>> listPointDataProcessLine(@Valid PointDataProcessLineReqVO pointDataProcessLineReqVO){
        List<PointDataProcessLineResVO> pointDataProcessLineResVOList = pointDataService.listPointDataProcessLine(pointDataProcessLineReqVO);
        return success(pointDataProcessLineResVOList);
    }

    @GetMapping("/distributionChart")
    @Operation(summary = "数据整编--监测图形--分布图")
    public CommonResult<List<PointDataDistributionChartResVO>> listPointDataDistributionChart(@Valid PointDataDistributionChartReqVO pointDataDistributionChartReqVO){
        List<PointDataDistributionChartResVO> pointDataDistributionChartResVOList = pointDataService.listPointDataDistributionChart(pointDataDistributionChartReqVO);
        return success(pointDataDistributionChartResVOList);
    }
}