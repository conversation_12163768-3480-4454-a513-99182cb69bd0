package cn.powerchina.bjy.link.dam.service.importconfig;

import java.util.*;

import cn.powerchina.bjy.link.dam.controller.admin.importfile.vo.ImportFileSaveReqVO;
import jakarta.validation.*;
import cn.powerchina.bjy.link.dam.controller.admin.importconfig.vo.*;
import cn.powerchina.bjy.link.dam.dal.dataobject.importconfig.ImportConfigDO;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageParam;

/**
 * 导入配置 Service 接口
 *
 * <AUTHOR>
 */
public interface ImportConfigService {

    /**
     * 创建导入配置
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createImportConfig(@Valid ImportFileSaveReqVO createReqVO);

    /**
     * 更新导入配置
     *
     * @param updateReqVO 更新信息
     */
    void updateImportConfig(@Valid ImportConfigSaveReqVO updateReqVO);

    /**
     * 删除导入配置
     *
     * @param id 编号
     */
    void deleteImportConfig(Long id);

    /**
    * 批量删除导入配置
    *
    * @param ids 编号
    */
    void deleteImportConfigListByIds(List<Long> ids);

    /**
     * 获得导入配置
     *
     * @param id 编号
     * @return 导入配置
     */
    ImportConfigDO getImportConfig(Long id);

    /**
     * 获得导入配置分页
     *
     * @param pageReqVO 分页查询
     * @return 导入配置分页
     */
    PageResult<ImportConfigDO> getImportConfigPage(ImportConfigPageReqVO pageReqVO);

}